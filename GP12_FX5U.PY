
import tkinter as tk
from pymodbus.client import ModbusTcpClient
from datetime import datetime
import logging
import threading
import time
import subprocess
import platform
import socket
import pyodbc  # Add pyodbc import for SQL Server connection

logging.basicConfig()
log = logging.getLogger()
log.setLevel(logging.ERROR)  # reduce pymodbus logs in GUI

PLC_IP = '*************'
PLC_PORT = 502
UNIT_ID = 1

# SQL Server connection configuration
SQL_SERVER = 'MSV\\SQLEXPRESS'
SQL_DATABASE = 'GP12'
SQL_DRIVER = '{ODBC Driver 17 for SQL Server}'

start_address = 5500
register_count = 42  # Read 40 registers for up to 40 characters

class PLCReaderApp:
    def __init__(self, root):
        self.root = root
        self.is_connected = False  # Move flag initialization to start
        self.root.title("FX5U PLC Data Reader")

        # Date and time labels at the top
        self.datetime_frame = tk.Frame(root)
        self.datetime_frame.pack(fill="x", pady=10)
        self.date_label = tk.Label(self.datetime_frame, text="", font=("Arial", 20, "bold"), anchor="w")

        self.date_label.pack(side="left", padx=10)
        self.time_label = tk.Label(self.datetime_frame, text="", font=("Arial", 20, "bold"), anchor="e")
        self.time_label.pack(side="right", padx=10)

        # Title label with bigger and bold font
        tk.Label(root, text="GP12 INSPECTION STANDARD BARCODE TRACEABILITY SYSTEMS",
                 font=("Arial", 30, "bold"), fg="blue").pack(pady=10)

        # PLC connection status with border
        self.status_frame = tk.Frame(root, highlightbackground="black", highlightthickness=1)
        self.status_frame.pack(anchor="w", padx=10, pady=10)
        self.status_label = tk.Label(self.status_frame, text="PLC Status: Disconnected",
                                     font=("Arial", 16, "bold"), fg="red", anchor="w")
        self.status_label.pack(side="left", padx=10)
        self.connection_info_label = tk.Label(self.status_frame, text="",
                                              font=("Arial", 16, "bold"), fg="blue", anchor="w")

        # Data Display Area with border for better visibility
        self.data_display_frame = tk.Frame(root, highlightbackground="black", highlightthickness=1)
        self.data_display_frame.pack(fill="x", padx=10, pady=(5, 10))

        tk.Label(self.data_display_frame, text="Barcode Data:",
                 font=("Arial", 20, "bold"), anchor="w").pack(side="left", padx=(10, 5))

        # Enhanced data display with better visibility and more space
        self.data_label = tk.Label(self.data_display_frame, text="---",
                                   font=("Arial", 25, "bold"), anchor="w", fg="purple", bg="white",
                                   wraplength=800, justify="left", padx=5, pady=5, height=2)
        self.data_label.pack(side="left", fill="x", expand=True, padx=(0, 10), pady=5)

        # Add a debug frame to show raw register values
        self.debug_frame = tk.Frame(root)
        self.debug_frame.pack(fill="x", padx=10, pady=5)

        # Increase button size and font for all buttons
        button_font = ("Arial", 14, "bold")  # Larger, bold font
        button_height = 2  # Increase button height

        self.debug_button = tk.Button(self.debug_frame, text="Show Raw Registers",
                                     command=self.toggle_debug_window,
                                     font=button_font, height=button_height)
        self.debug_button.pack(side="left", padx=10)

        # Add SQL Save button
        self.save_button = tk.Button(self.debug_frame, text="Save to Database",
                                    command=self.save_to_database, bg="#4CAF50", fg="white",
                                    font=button_font, height=button_height)
        self.save_button.pack(side="left", padx=10)

        # Add Manual Entry button
        self.manual_button = tk.Button(self.debug_frame, text="Manual Entry",
                                      command=self.show_manual_entry, bg="#2196F3", fg="white",
                                      font=button_font, height=button_height)
        self.manual_button.pack(side="left", padx=10)

        # Add View Database button
        self.view_db_button = tk.Button(self.debug_frame, text="Refresh Database View",
                                       command=self.refresh_database_display, bg="#FF9800", fg="white",
                                       font=button_font, height=button_height)
        self.view_db_button.pack(side="left", padx=10)

        # Add Search & Export button
        self.search_export_button = tk.Button(self.debug_frame, text="Search & Export",
                                             command=self.show_search_export_window, bg="#9C27B0", fg="white",

                                             font=button_font, height=button_height)
        self.search_export_button.pack(side="left", padx=10)

        # Add status label for database operations
        self.db_status_label = tk.Label(self.debug_frame, text="", font=("Arial", 10))
        self.db_status_label.pack(side="left", padx=10)

        self.debug_window = None
        self.manual_entry_window = None
        self.db_table_window = None  # Initialize database table window

        self.search_export_window = None  # Initialize search & export window

        # Create database records display section in main window
        self.create_database_display_section()

        # Add footer with running message
        self.footer_frame = tk.Frame(root, bg="#333333", height=30)
        self.footer_frame.pack(side="bottom", fill="x")

        self.footer_text = " Software developed by | MSV AUTOMATION SYSTEMS      CONTACT:- +91 9500004022     E-mail: <EMAIL> |                                                    "
        self.footer_label = tk.Label(self.footer_frame, text=self.footer_text, fg="white", bg="#333333",
                                    font=("Arial", 15, "bold"))
        self.footer_label.pack(pady=5)

        # Start the running message animation
        self.footer_position = 0
        self.animate_footer()

        # Attempt to connect to PLC
        self.socket_timeout = 0.5  # Add socket timeout setting
        self.client = ModbusTcpClient(PLC_IP, port=PLC_PORT, timeout=self.socket_timeout)

        self.client.unit_id = UNIT_ID

        self.connection = self.client.connect()

        if not self.connection:
            self.connection_info_label.config(text="Failed to connect to PLC", fg="red")
            self.connection_info_label.pack(side="left", padx=10) # Pack it even on failure
            self.update_status_label(connected=False) # Ensure status label is also updated
            return
        self.connection_info_label.config(text=f"at {PLC_IP}") # Shorter message
        self.connection_info_label.pack(side="left", padx=(0,10)) # Pack next to status
        self.update_status_label(connected=self.connection) # Update status label after connection attempt
        # Start reading in a thread to keep GUI responsive
        self.running = True
        threading.Thread(target=self.read_loop, daemon=True).start()

        # Start updating date and time
        self.update_datetime()

        # When window is closed, stop loop & close client
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        self.last_refresh = time.time()  # Initialize last refresh time
        self.last_data = ""  # Track the last data to detect changes
        self.current_barcode = ""  # Store the current barcode data

        # Cache for database column information
        self._db_columns_cache = None

    def get_database_columns(self, cursor):
        """Get database column information with caching"""
        if self._db_columns_cache is None:
            try:
                cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'parameter'")
                available_columns = [row[0] for row in cursor.fetchall()]

                # Determine the correct date column name
                date_column = None
                if 'DATE AND TIME' in available_columns:
                    date_column = "[DATE AND TIME]"
                elif 'DATE TIME' in available_columns:
                    date_column = "[DATE TIME]"
                else:
                    # Try to find any date-related column
                    date_candidates = [col for col in available_columns if 'DATE' in col.upper()]
                    if date_candidates:
                        date_column = f"[{date_candidates[0]}]"

                self._db_columns_cache = {
                    'available_columns': available_columns,
                    'has_model_name': 'MODEL_NAME' in available_columns,
                    'has_date_and_time': 'DATE AND TIME' in available_columns,
                    'has_date_time': 'DATE TIME' in available_columns,
                    'date_column': date_column or "S_NO"
                }

                print(f"Database columns cached: {self._db_columns_cache}")

            except Exception as e:
                print(f"Error getting column info: {e}")
                # Fallback cache
                self._db_columns_cache = {
                    'available_columns': ['S_NO', 'BARCODE_DATA'],
                    'has_model_name': False,
                    'has_date_and_time': False,
                    'has_date_time': False,
                    'date_column': "S_NO"
                }

        return self._db_columns_cache

    def update_datetime(self):
        now = datetime.now()
        self.date_label.config(text=now.strftime("%Y-%m-%d"))
        self.time_label.config(text=now.strftime("%H:%M:%S"))
        self.root.after(1000, self.update_datetime)  # Schedule the next update

    def update_status_label(self, connected):
        if connected:
            self.status_label.config(text="PLC Status: Connected", fg="green")
        else:
            self.status_label.config(text="PLC Status: Disconnected", fg="red")

    def check_plc_connection(self):
        try:
            # Quick socket test first
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.socket_timeout)
            result = sock.connect_ex((PLC_IP, PLC_PORT))
            sock.close()

            if result != 0:
                self.client.close()
                return False

            if not self.client.connected:
                if not self.client.connect():
                    return False

            # Verify Modbus communication using keyword arguments
            test_result = self.client.read_holding_registers(address=start_address, count=1)
            if test_result is None or test_result.isError():

                self.client.close()
                return False
            return True

        except (socket.error, Exception) as e:
            print(f"Connection check error: {e}")
            self.client.close()
            return False

    def read_loop(self):
        while self.running:
            try:
                # Check connection status
                current_status = self.check_plc_connection()

                # Update status immediately when connection state changes
                if current_status != self.is_connected:
                    self.is_connected = current_status
                    self.update_status_label(connected=current_status)
                    if not current_status:
                        self.root.after(0, self.update_data_display, "---") # Clear data on disconnect

                        continue  # Skip further processing if disconnected

                # Read registers continuously if connected
                if self.is_connected:
                    # Read registers D5500-D5539 (40 registers)

                    result = self.client.read_holding_registers(address=start_address, count=register_count)

                    if result is None or result.isError():
                        print(f"Modbus read error: {result}")
                        raise Exception("Failed to read registers")

                    values = result.registers

                    # Check if we have any non-zero values
                    non_zero_values = [v for v in values if v != 0]
                    if not non_zero_values:
                        print("All register values are zero!")

                    # Convert register values to barcode string
                    barcode_data = self.convert_to_barcode(values)

                    # Always update the display with the latest data

                    self.root.after(0, self.update_data_display, barcode_data)

                # Small sleep to prevent CPU overload but ensure continuous reading
                time.sleep(0.1)

            except Exception as e:
                # Handle disconnection or errors
                self.is_connected = False
                self.update_status_label(connected=False)
                self.root.after(0, self.update_data_display, "---")
                print(f"Read loop error: {e}")
                time.sleep(0.5)  # Wait a bit before retrying

    def convert_to_barcode(self, values):
        """Convert register values to a barcode string"""
        if not values:
            return "No barcode data available"

        # Print raw values for debugging
        print("\nRaw register values for barcode conversion:")
        for i in range(min(21, len(values))):  # Increased to 21 to include D5520
            val = values[i]
            char_val = chr(val) if 32 <= val <= 126 else '.'
            print(f"Index {i}: {val} (ASCII: {char_val})")

        # The correct format should be: 010:1104AAA07121N:DM216A:240525:02:0014
        # But we're getting: 10:01140AA0A1712:NMD12A62:0425:5200:104
        # This suggests the bytes are swapped or in wrong order

        # Extract ASCII characters from high and low bytes in CORRECT order
        barcode_string = ""
        for val in values:
            if val == 0:
                continue

            # Extract low byte FIRST, then high byte (reverse order)
            low_byte = val & 0xFF
            high_byte = (val >> 8) & 0xFF

            # Add printable characters in correct order
            if 32 <= low_byte <= 126:
                barcode_string += chr(low_byte)
            if 32 <= high_byte <= 126:
                barcode_string += chr(high_byte)

        # Clean up the barcode string
        # Remove any spaces and control characters
        barcode_string = ''.join(barcode_string.split())
        barcode_string = barcode_string.rstrip(' \t\r\n\0')

        # If the barcode is empty after processing, return a message
        if not barcode_string or barcode_string.isspace():
            print("No valid characters found in barcode data")
            return "No barcode data detected"

        # Check if the barcode matches the expected format
        # Expected format: 010:1104AAA07121N:DM216A:240525:02:0014
        expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'
        import re
        if re.search(expected_format, barcode_string):
            print(f"Found valid barcode format: {barcode_string}")
        else:
            print(f"Warning: Barcode may not match expected format: {barcode_string}")

        # Log the processed barcode string
        print(f"Processed barcode string: '{barcode_string}'")

        return barcode_string

    def on_close(self):
        self.running = False
        if self.connection:
            self.client.close()
        self.root.destroy()

    def update_data_display(self, data_text):
        """Update the data display with the latest PLC data"""
        # Store the current barcode data
        self.current_barcode = data_text

        # Check if the data matches our expected format
        import re
        # Updated pattern to match the actual format: 10:01140AA0A1712:NMD12A62:0425:5200:1014
        expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'

        if re.search(expected_format, data_text):
            # Valid barcode format - highlight in green
            self.data_label.config(text=data_text, bg="#e0ffe0", fg="black")  # Light green background
        elif data_text == "---" or data_text == "No barcode data detected":
            # No data - use default style
            self.data_label.config(text=data_text, bg="white", fg="red")
        else:
            # Data present but not in expected format - highlight in yellow
            self.data_label.config(text=data_text, bg="#ffffd0", fg="black")  # Light yellow background

    def toggle_debug_window(self):
        """Toggle the debug window to show raw register values"""
        if self.debug_window is None or not self.debug_window.winfo_exists():
            self.debug_window = tk.Toplevel(self.root)
            self.debug_window.title("Raw Register Values")
            self.debug_window.geometry("600x400")

            # Create a text widget to display register values
            self.debug_text = tk.Text(self.debug_window, font=("Courier New", 12))
            self.debug_text.pack(fill="both", expand=True, padx=10, pady=10)

            # Add a refresh button
            refresh_button = tk.Button(self.debug_window, text="Refresh",
                                      command=self.update_debug_window)
            refresh_button.pack(pady=10)

            # Initial update
            self.update_debug_window()
        else:
            self.debug_window.destroy()
            self.debug_window = None

    def update_debug_window(self):
        """Update the debug window with current register values"""
        if self.debug_window is None or not self.debug_window.winfo_exists():
            return

        if not self.is_connected:
            self.debug_text.delete(1.0, tk.END)
            self.debug_text.insert(tk.END, "PLC not connected")
            return

        try:
            # Read registers D5500-D5539
            result = self.client.read_holding_registers(address=start_address, count=register_count)
            if result is None or result.isError():
                self.debug_text.delete(1.0, tk.END)
                self.debug_text.insert(tk.END, "Failed to read registers")
                return

            values = result.registers

            # Clear the text widget
            self.debug_text.delete(1.0, tk.END)

            # Add header
            self.debug_text.insert(tk.END, "Register  Value  Hex      Low Byte   High Byte\n")
            self.debug_text.insert(tk.END, "-----------------------------------------------\n")

            # Add register values with byte breakdown
            has_data = False
            for i, val in enumerate(values):
                reg_addr = start_address + i
                hex_val = f"0x{val:04X}"
                low_byte = val & 0xFF
                high_byte = (val >> 8) & 0xFF
                low_char = chr(low_byte) if 32 <= low_byte <= 126 else '.'
                high_char = chr(high_byte) if 32 <= high_byte <= 126 else '.'

                line = f"D{reg_addr:<6}  {val:<5}  {hex_val:<8}  {low_byte:3} ({low_char})  {high_byte:3} ({high_char})\n"
                self.debug_text.insert(tk.END, line)
                if val != 0:
                    has_data = True

            if not has_data:
                self.debug_text.insert(tk.END, "\nWARNING: All registers contain zero values!\n")
                self.debug_text.insert(tk.END, "Check PLC configuration and data writing.\n")

            # Try different decoding methods
            self.debug_text.insert(tk.END, "\n--- Decoding Attempts ---\n")

            # Method 1: Extract ASCII from high/low bytes (original order)
            ascii_from_bytes_original = ""
            for val in values:
                if val == 0:
                    continue
                high_byte = (val >> 8) & 0xFF
                low_byte = val & 0xFF
                if 32 <= high_byte <= 126:
                    ascii_from_bytes_original += chr(high_byte)
                if 32 <= low_byte <= 126:
                    ascii_from_bytes_original += chr(low_byte)

            # Method 2: Extract ASCII from low/high bytes (reversed order)
            ascii_from_bytes_reversed = ""
            for val in values:
                if val == 0:
                    continue
                low_byte = val & 0xFF
                high_byte = (val >> 8) & 0xFF
                if 32 <= low_byte <= 126:
                    ascii_from_bytes_reversed += chr(low_byte)
                if 32 <= high_byte <= 126:
                    ascii_from_bytes_reversed += chr(high_byte)

            # Clean up the strings by removing spaces
            cleaned_original = ''.join(ascii_from_bytes_original.split())
            cleaned_reversed = ''.join(ascii_from_bytes_reversed.split())

            self.debug_text.insert(tk.END, f"Original byte order (High-Low): {cleaned_original}\n")
            self.debug_text.insert(tk.END, f"Reversed byte order (Low-High): {cleaned_reversed}\n\n")

            # Add the expected format for reference
            self.debug_text.insert(tk.END, "\nExpected Format Example:\n")
            self.debug_text.insert(tk.END, "010:1104AAA07121N:DM216A:240525:02:0014\n")

            # Check which format matches the expected pattern
            import re
            expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'

            if re.search(expected_format, cleaned_original):
                self.debug_text.insert(tk.END, "\nOriginal byte order matches expected format!\n")

            if re.search(expected_format, cleaned_reversed):
                self.debug_text.insert(tk.END, "\nReversed byte order matches expected format!\n")

        except Exception as e:
            self.debug_text.delete(1.0, tk.END)
            self.debug_text.insert(tk.END, f"Error: {e}")

    def save_to_database(self):
        """Save the current barcode data to SQL Server database"""
        # Get the current barcode data directly from the label
        barcode_data = self.data_label.cget("text")

        print(f"Attempting to save barcode data: '{barcode_data}'")

        # Don't save if no valid data
        if barcode_data == "---" or barcode_data == "No barcode data detected" or not barcode_data:
            self.show_db_status("No valid data to save", "red")
            return

        # Check if the data matches our expected format
        import re
        expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'
        if not re.search(expected_format, barcode_data):
            print(f"Warning: Barcode format may not be valid: '{barcode_data}'")
            # Continue anyway, but log the warning

        try:
            # Parse the barcode data to extract model information
            # Expected format: 010:1104AAA07121N:DM216A:240525:02:0014
            parts = barcode_data.split(':')
            if len(parts) < 3:
                self.show_db_status(f"Invalid barcode format: {barcode_data}", "red")
                return

            # Extract model name from the barcode (3rd segment)
            model_name = parts[2] if len(parts) > 2 else "UNKNOWN"

            # Calculate barcode length as integer
            barcode_size = len(barcode_data)

            # Get current date and time
            current_datetime = datetime.now()

            # Connect to SQL Server
            conn = None
            try:
                conn = pyodbc.connect(
                    f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                    f'Trusted_Connection=yes;'
                )

                cursor = conn.cursor()

                # Check what columns actually exist in the table
                cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'parameter'")
                available_columns = [row[0] for row in cursor.fetchall()]
                print(f"Available columns for insert: {available_columns}")

                # Determine the correct date column name
                date_column = None
                if 'DATE AND TIME' in available_columns:
                    date_column = "[DATE AND TIME]"
                elif 'DATE TIME' in available_columns:
                    date_column = "[DATE TIME]"
                else:
                    # Try to find any date-related column
                    date_candidates = [col for col in available_columns if 'DATE' in col.upper()]
                    if date_candidates:
                        date_column = f"[{date_candidates[0]}]"

                # Build dynamic insert query based on available columns
                insert_columns = []
                insert_values = []

                if date_column:
                    insert_columns.append(date_column)
                    insert_values.append(current_datetime)

                if 'MODEL_NAME' in available_columns:
                    insert_columns.append("MODEL_NAME")
                    insert_values.append(model_name)

                if 'BARCODE_DATA' in available_columns:
                    insert_columns.append("BARCODE_DATA")
                    insert_values.append(barcode_data)

                if 'BARCODE_SIZE' in available_columns:
                    insert_columns.append("BARCODE_SIZE")
                    insert_values.append("Standard")  # Default barcode size

                if 'REMARKES' in available_columns:
                    insert_columns.append("REMARKES")
                    insert_values.append("Automatically saved from PLC Reader")

                if not insert_columns:
                    self.show_db_status("No valid columns found in database table", "red")
                    return

                # Create the insert query
                placeholders = ', '.join(['?' for _ in insert_values])
                insert_query = f'''
                INSERT INTO dbo.parameter ({', '.join(insert_columns)})
                VALUES ({placeholders})
                '''

                print(f"Executing insert query: {insert_query}")
                print(f"With values: {insert_values}")

                # Execute the query
                cursor.execute(insert_query, insert_values)

                # Get the identity value that was generated
                cursor.execute("SELECT @@IDENTITY")
                identity_value = cursor.fetchone()[0]

                # Commit the transaction
                conn.commit()

                # Show success message with the generated identity
                self.show_db_status(f"Data saved successfully! ID: {identity_value}", "green")

                # Refresh the database display in main window
                self.refresh_database_display()

            except Exception as e:
                if conn:
                    conn.rollback()
                self.show_db_status(f"Database error: {str(e)}", "red")
                print(f"Database error: {e}")

            finally:
                if conn:
                    conn.close()

        except Exception as e:
            self.show_db_status(f"Error: {str(e)}", "red")
            print(f"Error saving to database: {e}")

    def show_db_status(self, message, color="black"):
        """Show database operation status message"""
        self.db_status_label.config(text=message, fg=color)
        # Clear the message after 5 seconds
        self.root.after(5000, lambda: self.db_status_label.config(text=""))

    def show_manual_entry(self):
        """Show a window for manual barcode data entry"""
        if self.manual_entry_window is None or not self.manual_entry_window.winfo_exists():
            self.manual_entry_window = tk.Toplevel(self.root)
            self.manual_entry_window.title("Manual Barcode Entry")
            self.manual_entry_window.geometry("600x300")

            # Create a frame for the form
            form_frame = tk.Frame(self.manual_entry_window, padx=20, pady=20)
            form_frame.pack(fill="both", expand=True)

            # Barcode data entry
            tk.Label(form_frame, text="Barcode Data:", font=("Arial", 12)).grid(row=0, column=0, sticky="w", pady=5)
            self.manual_barcode_entry = tk.Entry(form_frame, font=("Arial", 12), width=40)
            self.manual_barcode_entry.grid(row=0, column=1, sticky="ew", pady=5)
            self.manual_barcode_entry.insert(0, "010:1104AAA07121N:DM216A:240525:02:0014")  # Example format

            # Model name entry
            tk.Label(form_frame, text="Model Name:", font=("Arial", 12)).grid(row=1, column=0, sticky="w", pady=5)
            self.manual_model_entry = tk.Entry(form_frame, font=("Arial", 12), width=40)
            self.manual_model_entry.grid(row=1, column=1, sticky="ew", pady=5)

            # Barcode size dropdown
            tk.Label(form_frame, text="Barcode Size:", font=("Arial", 12)).grid(row=2, column=0, sticky="w", pady=5)
            self.barcode_size_var = tk.StringVar(value="Standard")
            barcode_sizes = ["Small", "Standard", "Large"]
            self.barcode_size_dropdown = tk.OptionMenu(form_frame, self.barcode_size_var, *barcode_sizes)
            self.barcode_size_dropdown.config(font=("Arial", 12), width=10)
            self.barcode_size_dropdown.grid(row=2, column=1, sticky="w", pady=5)

            # Remarks entry
            tk.Label(form_frame, text="Remarks:", font=("Arial", 12)).grid(row=3, column=0, sticky="w", pady=5)
            self.remarks_entry = tk.Entry(form_frame, font=("Arial", 12), width=40)
            self.remarks_entry.grid(row=3, column=1, sticky="ew", pady=5)
            self.remarks_entry.insert(0, "Manual entry")

            # Buttons frame
            buttons_frame = tk.Frame(form_frame)
            buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)

            # Save button
            save_button = tk.Button(buttons_frame, text="Save to Database",
                                   command=self.save_manual_entry, bg="#4CAF50", fg="white",
                                   font=("Arial", 12), padx=10, pady=5)
            save_button.pack(side="left", padx=10)

            # Cancel button
            cancel_button = tk.Button(buttons_frame, text="Cancel",
                                     command=self.manual_entry_window.destroy,
                                     font=("Arial", 12), padx=10, pady=5)
            cancel_button.pack(side="left", padx=10)

            # Status label
            self.manual_status_label = tk.Label(form_frame, text="", font=("Arial", 10))
            self.manual_status_label.grid(row=5, column=0, columnspan=2, pady=10)

            # Auto-fill model name from barcode when barcode changes
            self.manual_barcode_entry.bind("<KeyRelease>", self.auto_fill_model)
        else:
            self.manual_entry_window.lift()  # Bring window to front if already exists

    def auto_fill_model(self, event=None):
        """Auto-fill model name from barcode data"""
        barcode = self.manual_barcode_entry.get()
        parts = barcode.split(':')
        if len(parts) > 2:
            self.manual_model_entry.delete(0, tk.END)
            self.manual_model_entry.insert(0, parts[2])

    def save_manual_entry(self):
        """Save manually entered barcode data to database"""
        barcode_data = self.manual_barcode_entry.get().strip()
        model_name = self.manual_model_entry.get().strip()
        barcode_size = len(barcode_data)  # Use integer length
        remarks = self.remarks_entry.get().strip()

        if not barcode_data:
            self.manual_status_label.config(text="Barcode data is required", fg="red")
            return

        if not model_name:
            # Try to extract from barcode
            parts = barcode_data.split(':')
            if len(parts) > 2:
                model_name = parts[2]
            else:
                model_name = "UNKNOWN"

        # Connect to SQL Server
        conn = None
        try:
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )

            cursor = conn.cursor()

            # Check what columns actually exist in the table
            cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'parameter'")
            available_columns = [row[0] for row in cursor.fetchall()]
            print(f"Available columns for manual insert: {available_columns}")

            # Determine the correct date column name
            date_column = None
            if 'DATE AND TIME' in available_columns:
                date_column = "[DATE AND TIME]"
            elif 'DATE TIME' in available_columns:
                date_column = "[DATE TIME]"
            else:
                # Try to find any date-related column
                date_candidates = [col for col in available_columns if 'DATE' in col.upper()]
                if date_candidates:
                    date_column = f"[{date_candidates[0]}]"

            # Build dynamic insert query based on available columns
            insert_columns = []
            insert_values = []

            if date_column:
                insert_columns.append(date_column)
                insert_values.append(datetime.now())

            if 'MODEL_NAME' in available_columns:
                insert_columns.append("MODEL_NAME")
                insert_values.append(model_name)

            if 'BARCODE_DATA' in available_columns:
                insert_columns.append("BARCODE_DATA")
                insert_values.append(barcode_data)

            if 'BARCODE_SIZE' in available_columns:
                insert_columns.append("BARCODE_SIZE")
                insert_values.append(barcode_size)  # Use integer length

            if 'REMARKES' in available_columns:
                insert_columns.append("REMARKES")
                insert_values.append(remarks)

            if not insert_columns:
                self.manual_status_label.config(text="No valid columns found in database table", fg="red")
                return

            # Create the insert query
            placeholders = ', '.join(['?' for _ in insert_values])
            insert_query = f'''
            INSERT INTO dbo.parameter ({', '.join(insert_columns)})
            VALUES ({placeholders})
            '''

            print(f"Executing manual insert query: {insert_query}")
            print(f"With values: {insert_values}")

            # Execute the query
            cursor.execute(insert_query, insert_values)

            # Get the identity value that was generated
            cursor.execute("SELECT @@IDENTITY")
            identity_value = cursor.fetchone()[0]

            # Commit the transaction
            conn.commit()

            # Show success message with the generated identity
            self.manual_status_label.config(text=f"Data saved successfully! ID: {identity_value}", fg="green")

            # Update the main display with this barcode
            self.update_data_display(barcode_data)

            # Refresh the database display in main window
            self.refresh_database_display()

        except Exception as e:
            if conn:
                conn.rollback()
            self.manual_status_label.config(text=f"Database error: {str(e)}", fg="red")
            print(f"Database error: {e}")

        finally:
            if conn:
                conn.close()

    def animate_footer(self):
        """Animate the footer text to create a running message effect"""
        # Shift the text by one character
        self.footer_position = (self.footer_position + 1) % len(self.footer_text)
        shifted_text = self.footer_text[self.footer_position:] + self.footer_text[:self.footer_position]
        self.footer_label.config(text=shifted_text)

        # Schedule the next animation frame
        self.root.after(100, self.animate_footer)  # Update every 100ms for smooth animation

    def create_database_display_section(self):
        """Create the database records display section in the main window"""
        # Database records section
        self.db_section_frame = tk.Frame(self.root, highlightbackground="black", highlightthickness=2)
        self.db_section_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Title for database section - Remove the title
        title_frame = tk.Frame(self.db_section_frame, bg="#FF6600")  # Orange header
        title_frame.pack(fill="x")

        # Remove the label with the title text
        # No longer displaying "DATABASE RECORDS - GP12 INSPECTION TRACEABILITY"

        # Create scrollable frame for database records with BOTH horizontal and vertical scrolling
        # Create a container frame for proper scrollbar layout
        scrollbar_container = tk.Frame(self.db_section_frame)
        scrollbar_container.pack(fill="both", expand=True)

        # Create canvas and scrollbars
        self.db_canvas = tk.Canvas(scrollbar_container, height=300, bg="white")
        self.db_v_scrollbar = tk.Scrollbar(scrollbar_container, orient="vertical", command=self.db_canvas.yview)
        self.db_h_scrollbar = tk.Scrollbar(self.db_section_frame, orient="horizontal", command=self.db_canvas.xview)

        self.db_scrollable_frame = tk.Frame(self.db_canvas, bg="white")

        # Configure scrolling
        self.db_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.db_canvas.configure(scrollregion=self.db_canvas.bbox("all"))
        )

        self.db_canvas.create_window((0, 0), window=self.db_scrollable_frame, anchor="nw")
        self.db_canvas.configure(yscrollcommand=self.db_v_scrollbar.set)
        self.db_canvas.configure(xscrollcommand=self.db_h_scrollbar.set)

        # Pack canvas and scrollbars with proper layout
        self.db_canvas.pack(side="left", fill="both", expand=True)
        self.db_v_scrollbar.pack(side="right", fill="y")
        self.db_h_scrollbar.pack(side="bottom", fill="x")

        # Enhanced mouse wheel events for better scrolling experience
        self.db_canvas.bind("<MouseWheel>", self._on_mousewheel_vertical)
        self.db_canvas.bind("<Shift-MouseWheel>", self._on_mousewheel_horizontal)
        self.db_canvas.bind("<Button-4>", self._on_mousewheel_vertical)  # Linux support
        self.db_canvas.bind("<Button-5>", self._on_mousewheel_vertical)  # Linux support

        # Enable keyboard scrolling
        self.db_canvas.bind("<Up>", lambda e: self.db_canvas.yview_scroll(-1, "units"))
        self.db_canvas.bind("<Down>", lambda e: self.db_canvas.yview_scroll(1, "units"))
        self.db_canvas.bind("<Prior>", lambda e: self.db_canvas.yview_scroll(-10, "units"))  # Page Up
        self.db_canvas.bind("<Next>", lambda e: self.db_canvas.yview_scroll(10, "units"))   # Page Down

        # Make canvas focusable for keyboard events
        self.db_canvas.focus_set()

        # Load initial database data
        self.refresh_database_display()

    def _on_mousewheel_vertical(self, event):
        """Handle vertical mouse wheel scrolling"""
        self.db_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _on_mousewheel_horizontal(self, event):
        """Handle horizontal mouse wheel scrolling (with Shift key)"""
        self.db_canvas.xview_scroll(int(-1*(event.delta/120)), "units")

    def refresh_database_display(self):
        """Refresh the database display in the main window"""
        self.load_database_data_main_window()

    def load_database_data_main_window(self):
        """Load database data into the main window display"""
        # Clear existing data
        for widget in self.db_scrollable_frame.winfo_children():
            widget.destroy()

        try:
            # Connect to SQL Server
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )

            cursor = conn.cursor()

            # First, let's check what columns actually exist in the table
            has_model_name = False
            has_date_and_time = False
            has_date_time = False
            available_columns = []

            try:
                # Get column information
                cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'parameter'")
                available_columns = [row[0] for row in cursor.fetchall()]
                print(f"Available columns: {available_columns}")

                # Check for specific columns
                has_model_name = 'MODEL_NAME' in available_columns
                has_date_and_time = 'DATE AND TIME' in available_columns
                has_date_time = 'DATE TIME' in available_columns

                print(f"Column check - MODEL_NAME: {has_model_name}, DATE AND TIME: {has_date_and_time}, DATE TIME: {has_date_time}")

            except Exception as col_error:
                print(f"Error getting column info: {col_error}")

            # Determine the correct date column name
            date_column = None
            if has_date_and_time:
                date_column = "[DATE AND TIME]"
            elif has_date_time:
                date_column = "[DATE TIME]"
            else:
                # Try to find any date-related column
                date_candidates = [col for col in available_columns if 'DATE' in col.upper()]
                if date_candidates:
                    date_column = f"[{date_candidates[0]}]"
                else:
                    date_column = "S_NO"  # Fallback to S_NO if no date column found

            # Build dynamic query based on available columns
            select_columns = ["S_NO"]
            if date_column and date_column != "S_NO":
                select_columns.append(date_column)
            if has_model_name:
                select_columns.append("MODEL_NAME")

            # Always include these if they exist
            for col in ["BARCODE_DATA", "BARCODE_SIZE", "REMARKES"]:
                if col in available_columns:
                    select_columns.append(col)

            query = f"""
            SELECT TOP 50 {', '.join(select_columns)}
            FROM dbo.parameter
            ORDER BY {date_column} DESC
            """

            print(f"Executing query: {query}")
            cursor.execute(query)

            # Fetch all results and check if we have data
            all_rows = cursor.fetchall()
            print(f"Number of rows fetched: {len(all_rows)}")

            if not all_rows:
                # No data found - display message
                no_data_frame = tk.Frame(self.db_scrollable_frame, bg="#FFE0E0")
                no_data_frame.pack(fill="x", pady=20)

                no_data_label = tk.Label(no_data_frame, text="No data found in database table 'parameter'",
                                       font=("Arial", 16, "bold"), fg="red", bg="#FFE0E0", pady=20)
                no_data_label.pack()

                # Add a test data button
                test_button = tk.Button(no_data_frame, text="Add Test Data",
                                      command=self.add_test_data, bg="#4CAF50", fg="white",
                                      font=("Arial", 12, "bold"))
                test_button.pack(pady=10)

                conn.close()
                self.show_db_status("No data found in database", "orange")
                return

            # Create header row
            header_frame = tk.Frame(self.db_scrollable_frame, bg="#FF6600")  # Orange header
            header_frame.pack(fill="x", pady=2)

            # Build headers based on available columns
            headers = []
            col_widths = []

            for col in select_columns:
                if col == "S_NO":
                    headers.append("S_NO")
                    col_widths.append(10)
                elif "DATE" in col:
                    headers.append("DATE TIME")
                    col_widths.append(20)
                elif col == "MODEL_NAME":
                    headers.append("MODEL_NAME")
                    col_widths.append(15)
                elif col == "BARCODE_DATA":
                    headers.append("BARCODE_DATA")
                    col_widths.append(40)
                elif col == "BARCODE_SIZE":
                    headers.append("BARCODE_SIZE")
                    col_widths.append(15)
                elif col == "REMARKES":
                    headers.append("REMARKS")
                    col_widths.append(30)

            # Create header labels with CENTER alignment and fixed minimum widths
            for i, (header, width) in enumerate(zip(headers, col_widths)):
                # Increase minimum width for better visibility and ensure center alignment
                min_width = max(width, 12)  # Minimum width of 12 characters
                header_label = tk.Label(header_frame, text=header, font=("Arial", 16, "bold"),
                                      bg="#FF6600", fg="white", width=min_width, anchor="center",
                                      padx=8, pady=8, relief="ridge", borderwidth=1)
                header_label.pack(side="left", padx=1)

            # Add data rows with alternating colors and CENTER alignment
            for row_idx, row in enumerate(all_rows):  # Use the fetched data
                # Alternate between orange and white
                bg_color = "#FFE0B3" if row_idx % 2 == 0 else "white"  # Light orange and white

                row_frame = tk.Frame(self.db_scrollable_frame, bg=bg_color)
                row_frame.pack(fill="x", pady=1)

                # Display all available data with CENTER alignment
                for col_idx, (width, value) in enumerate(zip(col_widths, row)):
                    display_value = str(value) if value is not None else ""

                    # Format datetime for better readability
                    if col_idx < len(headers) and value and "DATE" in headers[col_idx]:  # Date column
                        try:
                            if hasattr(value, 'strftime'):
                                display_value = value.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            pass

                    # Don't truncate text - let horizontal scrolling handle long content
                    # This ensures all data is visible with horizontal scrolling

                    # IMPORTANT: CENTER alignment for ALL columns as requested
                    min_width = max(width, 12)  # Minimum width of 12 characters

                    data_label = tk.Label(row_frame, text=display_value, font=("Arial", 14, "bold"),
                                        bg=bg_color, width=min_width, anchor="center",
                                        padx=8, pady=4, relief="ridge", borderwidth=1)
                    data_label.pack(side="left", padx=1)

            print(f"Successfully displayed {len(all_rows)} rows")

            conn.close()

            # Update status
            self.show_db_status("Database refreshed successfully", "green")

        except Exception as e:
            # Show error message
            error_frame = tk.Frame(self.db_scrollable_frame, bg="#FFE0E0")
            error_frame.pack(fill="x", pady=10)

            error_label = tk.Label(error_frame, text=f"Error loading database: {str(e)}",
                                  font=("Arial", 14, "bold"), fg="red", bg="#FFE0E0", pady=10)
            error_label.pack()

            self.show_db_status(f"Database error: {str(e)}", "red")
            print(f"Database error: {e}")

    def add_test_data(self):
        """Add some test data to the database for demonstration"""
        # Sample test data
        test_data = [
            ("DM216A", "010:1104AAA07121N:DM216A:240525:02:0014", "Standard", "Test data 1"),
            ("DM216B", "010:1104BBB07122N:DM216B:240525:02:0015", "Standard", "Test data 2"),
            ("DM216C", "010:1104CCC07123N:DM216C:240525:02:0016", "Large", "Test data 3"),
            ("DM216D", "010:1104DDD07124N:DM216D:240525:02:0017", "Standard", "Test data 4"),
            ("DM216E", "010:1104EEE07125N:DM216E:240525:02:0018", "Small", "Test data 5")
        ]

        try:
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )
            cursor = conn.cursor()

            # Get column info
            col_info = self.get_database_columns(cursor)
            available_columns = col_info['available_columns']
            date_column = col_info['date_column']

            # Build dynamic insert query
            insert_columns = []
            if date_column and date_column != "S_NO":
                insert_columns.append(date_column)
            if 'MODEL_NAME' in available_columns:
                insert_columns.append("MODEL_NAME")
            if 'BARCODE_DATA' in available_columns:
                insert_columns.append("BARCODE_DATA")
            if 'BARCODE_SIZE' in available_columns:
                insert_columns.append("BARCODE_SIZE")
            if 'REMARKES' in available_columns:
                insert_columns.append("REMARKES")

            if not insert_columns:
                self.show_db_status("Cannot add test data - no valid columns", "red")
                return

            # Insert test data
            from datetime import datetime, timedelta
            base_time = datetime.now()

            for i, (model, barcode, size, remarks) in enumerate(test_data):
                insert_values = []

                if date_column and date_column != "S_NO":
                    # Add different times for each record
                    record_time = base_time - timedelta(hours=i)
                    insert_values.append(record_time)

                if 'MODEL_NAME' in available_columns:
                    insert_values.append(model)
                if 'BARCODE_DATA' in available_columns:
                    insert_values.append(barcode)
                if 'BARCODE_SIZE' in available_columns:
                    insert_values.append(size)
                if 'REMARKES' in available_columns:
                    insert_values.append(remarks)

                # Create insert query
                placeholders = ', '.join(['?' for _ in insert_values])
                insert_query = f'''
                INSERT INTO dbo.parameter ({', '.join(insert_columns)})
                VALUES ({placeholders})
                '''

                cursor.execute(insert_query, insert_values)

            conn.commit()
            conn.close()

            self.show_db_status(f"Added {len(test_data)} test records successfully", "green")

            # Refresh the display
            self.refresh_database_display()

        except Exception as e:
            self.show_db_status(f"Error adding test data: {str(e)}", "red")
            print(f"Test data error: {e}")

    def show_database_table(self):
        """Show a window with the database table contents"""
        if self.db_table_window is None or not self.db_table_window.winfo_exists():
            self.db_table_window = tk.Toplevel(self.root)
            self.db_table_window.title("Database Records")
            self.db_table_window.geometry("1200x600")

            # Create a frame for the table
            table_frame = tk.Frame(self.db_table_window)
            table_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Add a title
            tk.Label(table_frame, text="GP12 Database Records",
                    font=("Arial", 16, "bold")).pack(pady=(0, 10))

            # Create a frame for the table header
            header_frame = tk.Frame(table_frame, bg="#4CAF50")
            header_frame.pack(fill="x")

            # Define column widths
            col_widths = {
                "S_NO": 8,
                "DATE TIME": 20,
                "MODEL_NAME": 15,
                "BARCODE_DATA": 40,
                "BARCODE_SIZE": 12,
                "REMARKES": 30
            }

            # Create header labels with CENTER alignment
            for i, (col_name, width) in enumerate(col_widths.items()):
                min_width = max(width, 12)  # Minimum width of 12 characters
                tk.Label(header_frame, text=col_name, font=("Arial", 12, "bold"),
                        bg="#4CAF50", fg="white", width=min_width, anchor="center",
                        padx=5, relief="ridge", borderwidth=1).grid(row=0, column=i, sticky="ew", padx=1)

            # Create a canvas for BOTH horizontal and vertical scrolling
            canvas = tk.Canvas(table_frame)
            v_scrollbar = tk.Scrollbar(table_frame, orient="vertical", command=canvas.yview)
            h_scrollbar = tk.Scrollbar(table_frame, orient="horizontal", command=canvas.xview)
            scrollable_frame = tk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=v_scrollbar.set)
            canvas.configure(xscrollcommand=h_scrollbar.set)

            # Pack with proper layout for both scrollbars
            canvas.pack(side="top", fill="both", expand=True)
            v_scrollbar.pack(side="right", fill="y")
            h_scrollbar.pack(side="bottom", fill="x")

            # Bind mouse wheel events for better scrolling experience
            canvas.bind("<MouseWheel>", lambda event: canvas.yview_scroll(int(-1*(event.delta/120)), "units"))
            canvas.bind("<Shift-MouseWheel>", lambda event: canvas.xview_scroll(int(-1*(event.delta/120)), "units"))

            # Add refresh button
            refresh_button = tk.Button(self.db_table_window, text="Refresh Data",
                                      command=lambda: self.load_database_data(scrollable_frame, col_widths),
                                      bg="#2196F3", fg="white", font=("Arial", 12))
            refresh_button.pack(pady=10)

            # Load data initially
            self.load_database_data(scrollable_frame, col_widths)
        else:
            self.db_table_window.lift()  # Bring window to front if already exists

    def load_database_data(self, frame, col_widths):
        """Load data from database into the table frame"""
        # Clear existing data
        for widget in frame.winfo_children():
            widget.destroy()

        try:
            # Connect to SQL Server
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )

            cursor = conn.cursor()

            # Check what columns actually exist in the table
            cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'parameter'")
            available_columns = [row[0] for row in cursor.fetchall()]
            print(f"Available columns for table view: {available_columns}")

            # Determine the correct date column name
            date_column = None
            if 'DATE AND TIME' in available_columns:
                date_column = "[DATE AND TIME]"
            elif 'DATE TIME' in available_columns:
                date_column = "[DATE TIME]"
            else:
                # Try to find any date-related column
                date_candidates = [col for col in available_columns if 'DATE' in col.upper()]
                if date_candidates:
                    date_column = f"[{date_candidates[0]}]"
                else:
                    date_column = "S_NO"  # Fallback to S_NO if no date column found

            # Build dynamic query based on available columns
            select_columns = ["S_NO"]
            if date_column and date_column != "S_NO":
                select_columns.append(date_column)
            if 'MODEL_NAME' in available_columns:
                select_columns.append("MODEL_NAME")

            # Always include these if they exist
            for col in ["BARCODE_DATA", "BARCODE_SIZE", "REMARKES"]:
                if col in available_columns:
                    select_columns.append(col)

            query = f"""
            SELECT TOP 100 {', '.join(select_columns)}
            FROM dbo.parameter
            ORDER BY {date_column} DESC
            """

            print(f"Executing table view query: {query}")
            cursor.execute(query)

            # Add data rows
            for row_idx, row in enumerate(cursor.fetchall()):
                bg_color = "#f0f0f0" if row_idx % 2 == 0 else "white"  # Alternating row colors

                for col_idx, (col_name, width) in enumerate(col_widths.items()):
                    if col_idx < len(row):  # Make sure we don't exceed the row data
                        value = str(row[col_idx]) if row[col_idx] is not None else ""
                        if "DATE" in col_name and value:
                            # Format datetime for better readability
                            try:
                                if hasattr(row[col_idx], 'strftime'):
                                    value = row[col_idx].strftime("%Y-%m-%d %H:%M:%S")
                            except:
                                pass

                        # IMPORTANT: CENTER alignment for ALL columns as requested
                        min_width = max(width, 12)  # Minimum width of 12 characters
                        label = tk.Label(frame, text=value, font=("Arial", 10, "bold"),
                                        bg=bg_color, width=min_width, anchor="center", padx=5,
                                        relief="ridge", borderwidth=1)
                        label.grid(row=row_idx, column=col_idx, sticky="ew", pady=2, padx=1)

            conn.close()

        except Exception as e:
            # Show error message in the table
            error_label = tk.Label(frame, text=f"Error loading data: {str(e)}",
                                  font=("Arial", 12), fg="red", pady=20)
            error_label.grid(row=0, column=0, columnspan=len(col_widths))
            print(f"Database error: {e}")

    def show_search_export_window(self):
        """Show the Search & Export window"""
        if self.search_export_window is None or not self.search_export_window.winfo_exists():
            self.search_export_window = tk.Toplevel(self.root)
            self.search_export_window.title("Search & Export - GP12 Database")
            self.search_export_window.geometry("1400x800")
            self.search_export_window.configure(bg="#f0f0f0")

            # Main container frame
            main_frame = tk.Frame(self.search_export_window, bg="#f0f0f0")
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Title
            title_label = tk.Label(main_frame, text="🔍 SEARCH & EXPORT - GP12 INSPECTION DATABASE",
                                 font=("Arial", 20, "bold"), fg="#2E7D32", bg="#f0f0f0")
            title_label.pack(pady=(0, 20))

            # Search criteria frame
            search_frame = tk.LabelFrame(main_frame, text="Search Criteria", font=("Arial", 14, "bold"),
                                       fg="#1976D2", bg="#f0f0f0", padx=10, pady=10)
            search_frame.pack(fill="x", pady=(0, 10))

            # Create search fields in a grid
            search_grid = tk.Frame(search_frame, bg="#f0f0f0")
            search_grid.pack(fill="x")

            # Row 1: Date range
            tk.Label(search_grid, text="Date From:", font=("Arial", 12, "bold"), bg="#f0f0f0").grid(row=0, column=0, sticky="w", padx=5, pady=5)
            self.date_from_entry = tk.Entry(search_grid, font=("Arial", 12), width=15)
            self.date_from_entry.grid(row=0, column=1, padx=5, pady=5)
            self.date_from_entry.insert(0, "2024-01-01")

            tk.Label(search_grid, text="Date To:", font=("Arial", 12, "bold"), bg="#f0f0f0").grid(row=0, column=2, sticky="w", padx=5, pady=5)
            self.date_to_entry = tk.Entry(search_grid, font=("Arial", 12), width=15)
            self.date_to_entry.grid(row=0, column=3, padx=5, pady=5)
            from datetime import datetime
            self.date_to_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

            # Row 2: Model and Barcode search
            tk.Label(search_grid, text="Model Name:", font=("Arial", 12, "bold"), bg="#f0f0f0").grid(row=1, column=0, sticky="w", padx=5, pady=5)
            self.model_search_entry = tk.Entry(search_grid, font=("Arial", 12), width=15)
            self.model_search_entry.grid(row=1, column=1, padx=5, pady=5)

            tk.Label(search_grid, text="Barcode Data:", font=("Arial", 12, "bold"), bg="#f0f0f0").grid(row=1, column=2, sticky="w", padx=5, pady=5)
            self.barcode_search_entry = tk.Entry(search_grid, font=("Arial", 12), width=20)
            self.barcode_search_entry.grid(row=1, column=3, padx=5, pady=5)

            # Row 3: Record limit and search buttons
            tk.Label(search_grid, text="Max Records:", font=("Arial", 12, "bold"), bg="#f0f0f0").grid(row=2, column=0, sticky="w", padx=5, pady=5)
            self.limit_var = tk.StringVar(value="1000")
            limit_combo = tk.OptionMenu(search_grid, self.limit_var, "100", "500", "1000", "5000", "All")
            limit_combo.config(font=("Arial", 12), width=8)
            limit_combo.grid(row=2, column=1, padx=5, pady=5, sticky="w")

            # Search and Clear buttons
            search_btn = tk.Button(search_grid, text="🔍 SEARCH", command=self.perform_search,
                                 bg="#4CAF50", fg="white", font=("Arial", 12, "bold"), padx=20)
            search_btn.grid(row=2, column=2, padx=10, pady=5)

            clear_btn = tk.Button(search_grid, text="🗑️ CLEAR", command=self.clear_search,
                                bg="#F44336", fg="white", font=("Arial", 12, "bold"), padx=20)
            clear_btn.grid(row=2, column=3, padx=10, pady=5)

            # Results frame with enhanced scrolling
            results_frame = tk.LabelFrame(main_frame, text="Search Results", font=("Arial", 14, "bold"),
                                        fg="#1976D2", bg="#f0f0f0", padx=5, pady=5)
            results_frame.pack(fill="both", expand=True, pady=(10, 0))

            # Create enhanced scrollable results area
            self.create_search_results_area(results_frame)

            # Export buttons frame
            export_frame = tk.Frame(main_frame, bg="#f0f0f0")
            export_frame.pack(fill="x", pady=(10, 0))

            # Export buttons
            export_csv_btn = tk.Button(export_frame, text="📊 Export to CSV", command=self.export_to_csv,
                                     bg="#FF9800", fg="white", font=("Arial", 12, "bold"), padx=20)
            export_csv_btn.pack(side="left", padx=10)

            export_excel_btn = tk.Button(export_frame, text="📈 Export to Excel", command=self.export_to_excel,
                                       bg="#4CAF50", fg="white", font=("Arial", 12, "bold"), padx=20)
            export_excel_btn.pack(side="left", padx=10)

            print_btn = tk.Button(export_frame, text="🖨️ Print Results", command=self.print_results,
                                bg="#2196F3", fg="white", font=("Arial", 12, "bold"), padx=20)
            print_btn.pack(side="left", padx=10)

            # Status label for search operations
            self.search_status_label = tk.Label(export_frame, text="Ready to search...",
                                              font=("Arial", 10), fg="#666", bg="#f0f0f0")
            self.search_status_label.pack(side="right", padx=10)

            # Load initial data
            self.perform_search()

        else:
            self.search_export_window.lift()  # Bring window to front if already exists

    def create_search_results_area(self, parent_frame):
        """Create the search results area with enhanced vertical and horizontal scrolling"""
        # Create container for scrollbars and canvas
        container = tk.Frame(parent_frame, bg="#f0f0f0")
        container.pack(fill="both", expand=True)

        # Create canvas and scrollbars for BOTH vertical and horizontal scrolling
        self.search_canvas = tk.Canvas(container, bg="white", highlightthickness=1, highlightbackground="#ccc")

        # BOTH vertical and horizontal scrollbars
        self.search_v_scrollbar = tk.Scrollbar(container, orient="vertical", command=self.search_canvas.yview)
        self.search_h_scrollbar = tk.Scrollbar(container, orient="horizontal", command=self.search_canvas.xview)

        # Scrollable frame for results
        self.search_results_frame = tk.Frame(self.search_canvas, bg="white")

        # Configure scrolling
        self.search_results_frame.bind(
            "<Configure>",
            lambda e: self.search_canvas.configure(scrollregion=self.search_canvas.bbox("all"))
        )

        self.search_canvas.create_window((0, 0), window=self.search_results_frame, anchor="nw")
        self.search_canvas.configure(yscrollcommand=self.search_v_scrollbar.set)
        self.search_canvas.configure(xscrollcommand=self.search_h_scrollbar.set)

        # Pack with proper layout for BOTH scrollbars
        self.search_canvas.pack(side="left", fill="both", expand=True)
        self.search_v_scrollbar.pack(side="right", fill="y")
        self.search_h_scrollbar.pack(side="bottom", fill="x")

        # Enhanced mouse wheel and keyboard support
        self.search_canvas.bind("<MouseWheel>", lambda e: self.search_canvas.yview_scroll(int(-1*(e.delta/120)), "units"))
        self.search_canvas.bind("<Shift-MouseWheel>", lambda e: self.search_canvas.xview_scroll(int(-1*(e.delta/120)), "units"))
        self.search_canvas.bind("<Button-4>", lambda e: self.search_canvas.yview_scroll(-1, "units"))  # Linux
        self.search_canvas.bind("<Button-5>", lambda e: self.search_canvas.yview_scroll(1, "units"))   # Linux

        # Keyboard scrolling support
        self.search_canvas.bind("<Up>", lambda e: self.search_canvas.yview_scroll(-1, "units"))
        self.search_canvas.bind("<Down>", lambda e: self.search_canvas.yview_scroll(1, "units"))
        self.search_canvas.bind("<Left>", lambda e: self.search_canvas.xview_scroll(-1, "units"))
        self.search_canvas.bind("<Right>", lambda e: self.search_canvas.xview_scroll(1, "units"))
        self.search_canvas.bind("<Prior>", lambda e: self.search_canvas.yview_scroll(-10, "units"))  # Page Up
        self.search_canvas.bind("<Next>", lambda e: self.search_canvas.yview_scroll(10, "units"))   # Page Down

        # Make canvas focusable
        self.search_canvas.focus_set()

        # Store search results for export
        self.current_search_results = []

    def perform_search(self):
        """Perform database search based on criteria"""
        try:
            # Clear previous results
            for widget in self.search_results_frame.winfo_children():
                widget.destroy()

            # Get search criteria
            date_from = self.date_from_entry.get().strip()
            date_to = self.date_to_entry.get().strip()
            model_search = self.model_search_entry.get().strip()
            barcode_search = self.barcode_search_entry.get().strip()
            limit = self.limit_var.get()

            # Update status
            self.search_status_label.config(text="Searching...", fg="blue")
            self.search_export_window.update()

            # Connect to database
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )
            cursor = conn.cursor()

            # Get column info directly for search
            cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'parameter'")
            available_columns = [row[0] for row in cursor.fetchall()]
            print(f"Search - Available columns: {available_columns}")

            # Determine the correct date column name
            date_column = None
            if 'DATE AND TIME' in available_columns:
                date_column = "[DATE AND TIME]"
            elif 'DATE TIME' in available_columns:
                date_column = "[DATE TIME]"
            else:
                # Try to find any date-related column
                date_candidates = [col for col in available_columns if 'DATE' in col.upper()]
                if date_candidates:
                    date_column = f"[{date_candidates[0]}]"
                else:
                    date_column = "S_NO"  # Fallback to S_NO if no date column found

            # Build dynamic query
            select_columns = ["S_NO"]
            if date_column and date_column != "S_NO":
                select_columns.append(date_column)
            if 'MODEL_NAME' in available_columns:
                select_columns.append("MODEL_NAME")
            for col in ["BARCODE_DATA", "BARCODE_SIZE", "REMARKES"]:
                if col in available_columns:
                    select_columns.append(col)

            # Build WHERE clause
            where_conditions = []
            params = []

            if date_from and date_column != "S_NO":
                where_conditions.append(f"{date_column} >= ?")
                params.append(date_from)

            if date_to and date_column != "S_NO":
                where_conditions.append(f"{date_column} <= ?")
                params.append(date_to + " 23:59:59")

            if model_search and 'MODEL_NAME' in available_columns:
                where_conditions.append("MODEL_NAME LIKE ?")
                params.append(f"%{model_search}%")

            if barcode_search and 'BARCODE_DATA' in available_columns:
                where_conditions.append("BARCODE_DATA LIKE ?")
                params.append(f"%{barcode_search}%")

            # Construct final query
            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            limit_clause = f" TOP {limit}" if limit != "All" else ""

            query = f"""
            SELECT{limit_clause} {', '.join(select_columns)}
            FROM dbo.parameter{where_clause}
            ORDER BY {date_column} DESC
            """

            print(f"Search query: {query}")
            print(f"Parameters: {params}")

            cursor.execute(query, params)
            results = cursor.fetchall()

            # Store results for export
            self.current_search_results = results
            self.current_search_columns = select_columns

            # Display results
            self.display_search_results(results, select_columns)

            conn.close()

            # Update status
            self.search_status_label.config(text=f"Found {len(results)} records", fg="green")

        except Exception as e:
            self.search_status_label.config(text=f"Search error: {str(e)}", fg="red")
            print(f"Search error: {e}")

    def display_search_results(self, results, columns):
        """Display search results in the results frame with center alignment"""
        # Clear existing results first
        for widget in self.search_results_frame.winfo_children():
            widget.destroy()

        if not results:
            no_results_label = tk.Label(self.search_results_frame, text="No records found matching your criteria",
                                      font=("Arial", 16, "bold"), fg="#666", bg="white", pady=50)
            no_results_label.pack()
            return

        # Create header row with CENTER alignment - SAME DESIGN AS MAIN WINDOW (ORANGE)
        header_frame = tk.Frame(self.search_results_frame, bg="#FF6600")  # Orange header like main window
        header_frame.pack(fill="x", pady=2)

        # Define column headers and widths - SAME AS MAIN WINDOW
        headers = []
        col_widths = []

        for col in columns:
            if col == "S_NO":
                headers.append("S_NO")
                col_widths.append(10)
            elif "DATE" in col:
                headers.append("DATE TIME")
                col_widths.append(20)
            elif col == "MODEL_NAME":
                headers.append("MODEL_NAME")
                col_widths.append(15)
            elif col == "BARCODE_DATA":
                headers.append("BARCODE_DATA")
                col_widths.append(40)
            elif col == "BARCODE_SIZE":
                headers.append("BARCODE_SIZE")
                col_widths.append(15)
            elif col == "REMARKES":
                headers.append("REMARKS")
                col_widths.append(30)

        # Create header labels with CENTER alignment - SAME STYLE AS MAIN WINDOW
        for i, (header, width) in enumerate(zip(headers, col_widths)):
            min_width = max(width, 12)  # Minimum width of 12 characters
            header_label = tk.Label(header_frame, text=header, font=("Arial", 16, "bold"),
                                  bg="#FF6600", fg="white", width=min_width, anchor="center",
                                  padx=8, pady=8, relief="ridge", borderwidth=1)
            header_label.pack(side="left", padx=1)

        # Add data rows with alternating colors and CENTER alignment - SAME AS MAIN WINDOW
        for row_idx, row in enumerate(results):
            # Alternate between orange and white - SAME AS MAIN WINDOW
            bg_color = "#FFE0B3" if row_idx % 2 == 0 else "white"  # Light orange and white
            row_frame = tk.Frame(self.search_results_frame, bg=bg_color)
            row_frame.pack(fill="x", pady=1)

            # Display all available data with CENTER alignment - SAME STYLE AS MAIN WINDOW
            for col_idx, (value, width) in enumerate(zip(row, col_widths)):
                display_value = str(value) if value is not None else ""

                # Format datetime for better readability
                if col_idx < len(headers) and "DATE" in headers[col_idx] and value:
                    try:
                        if hasattr(value, 'strftime'):
                            display_value = value.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        pass

                # IMPORTANT: CENTER alignment for ALL columns - SAME AS MAIN WINDOW
                min_width = max(width, 12)  # Minimum width of 12 characters

                data_label = tk.Label(row_frame, text=display_value, font=("Arial", 14, "bold"),
                                    bg=bg_color, width=min_width, anchor="center",
                                    padx=8, pady=4, relief="ridge", borderwidth=1)
                data_label.pack(side="left", padx=1)

        print(f"Successfully displayed {len(results)} records")

    def clear_search(self):
        """Clear all search fields"""
        self.date_from_entry.delete(0, tk.END)
        self.date_from_entry.insert(0, "2024-01-01")

        self.date_to_entry.delete(0, tk.END)
        from datetime import datetime
        self.date_to_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        self.model_search_entry.delete(0, tk.END)
        self.barcode_search_entry.delete(0, tk.END)
        self.limit_var.set("1000")

        self.search_status_label.config(text="Search cleared", fg="blue")

        # Perform fresh search
        self.perform_search()

    def export_to_csv(self):
        """Export search results to CSV file"""
        try:
            if not hasattr(self, 'current_search_results') or not self.current_search_results:
                self.search_status_label.config(text="No data to export", fg="red")
                return

            import csv
            from tkinter import filedialog
            from datetime import datetime

            # Ask user for file location
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Save CSV Export",
                initialname=f"GP12_Export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )

            if not filename:
                return

            # Write CSV file
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write headers
                headers = []
                for col in self.current_search_columns:
                    if col == "S_NO":
                        headers.append("S_NO")
                    elif "DATE" in col:
                        headers.append("DATE_TIME")
                    elif col == "MODEL_NAME":
                        headers.append("MODEL_NAME")
                    elif col == "BARCODE_DATA":
                        headers.append("BARCODE_DATA")
                    elif col == "BARCODE_SIZE":
                        headers.append("BARCODE_SIZE")
                    elif col == "REMARKES":
                        headers.append("REMARKS")

                writer.writerow(headers)

                # Write data rows
                for row in self.current_search_results:
                    formatted_row = []
                    for i, value in enumerate(row):
                        if value is None:
                            formatted_row.append("")
                        elif hasattr(value, 'strftime'):  # DateTime
                            formatted_row.append(value.strftime("%Y-%m-%d %H:%M:%S"))
                        else:
                            formatted_row.append(str(value))
                    writer.writerow(formatted_row)

            self.search_status_label.config(text=f"Exported {len(self.current_search_results)} records to CSV", fg="green")

        except Exception as e:
            self.search_status_label.config(text=f"Export error: {str(e)}", fg="red")
            print(f"CSV export error: {e}")

    def export_to_excel(self):
        """Export search results to Excel file"""
        try:
            if not hasattr(self, 'current_search_results') or not self.current_search_results:
                self.search_status_label.config(text="No data to export", fg="red")
                return

            try:
                import openpyxl
                from openpyxl.styles import Font, PatternFill, Alignment
            except ImportError:
                self.search_status_label.config(text="openpyxl not installed. Install with: pip install openpyxl", fg="red")
                return

            from tkinter import filedialog
            from datetime import datetime

            # Ask user for file location
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Save Excel Export",
                initialname=f"GP12_Export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                return

            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "GP12 Inspection Data"

            # Define styles
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            center_alignment = Alignment(horizontal="center", vertical="center")

            # Write headers
            headers = []
            for col in self.current_search_columns:
                if col == "S_NO":
                    headers.append("S_NO")
                elif "DATE" in col:
                    headers.append("DATE_TIME")
                elif col == "MODEL_NAME":
                    headers.append("MODEL_NAME")
                elif col == "BARCODE_DATA":
                    headers.append("BARCODE_DATA")
                elif col == "BARCODE_SIZE":
                    headers.append("BARCODE_SIZE")
                elif col == "REMARKES":
                    headers.append("REMARKS")

            # Write headers with styles
            for col_idx, header in enumerate(headers):
                cell = ws.cell(row=1, column=col_idx+1, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # Write data rows
            for row_idx, row in enumerate(self.current_search_results, start=2):
                for col_idx, value in enumerate(row):
                    cell = ws.cell(row=row_idx, column=col_idx+1, value=value)
                    cell.alignment = center_alignment

            # Save the workbook
            wb.save(filename)

            self.search_status_label.config(text=f"Exported {len(self.current_search_results)} records to Excel", fg="green")

        except Exception as e:
            self.search_status_label.config(text=f"Export error: {str(e)}", fg="red")
            print(f"Excel export error: {e}")

    def print_results(self):
        """Print search results"""
        try:
            if not hasattr(self, 'current_search_results') or not self.current_search_results:
                self.search_status_label.config(text="No data to print", fg="red")
                return

            # Create a simple print preview window
            print_window = tk.Toplevel(self.search_export_window)
            print_window.title("Print Preview - GP12 Search Results")
            print_window.geometry("800x600")

            # Create text widget for print content
            text_widget = tk.Text(print_window, font=("Courier New", 10), wrap=tk.NONE)
            text_widget.pack(fill="both", expand=True, padx=10, pady=10)

            # Add scrollbars
            v_scroll = tk.Scrollbar(print_window, orient="vertical", command=text_widget.yview)
            h_scroll = tk.Scrollbar(print_window, orient="horizontal", command=text_widget.xview)
            text_widget.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
            v_scroll.pack(side="right", fill="y")
            h_scroll.pack(side="bottom", fill="x")

            # Format content for printing
            content = "GP12 INSPECTION DATABASE - SEARCH RESULTS\n"
            content += "=" * 80 + "\n"
            content += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            content += f"Total Records: {len(self.current_search_results)}\n"
            content += "=" * 80 + "\n\n"

            # Add headers
            headers = []
            for col in self.current_search_columns:
                if col == "S_NO":
                    headers.append("S_NO")
                elif "DATE" in col:
                    headers.append("DATE_TIME")
                elif col == "MODEL_NAME":
                    headers.append("MODEL_NAME")
                elif col == "BARCODE_DATA":
                    headers.append("BARCODE_DATA")
                elif col == "BARCODE_SIZE":
                    headers.append("SIZE")
                elif col == "REMARKES":
                    headers.append("REMARKS")

            # Format as table
            col_widths = [8, 20, 15, 40, 8, 20]  # Adjust based on content
            header_line = ""
            for i, header in enumerate(headers):
                if i < len(col_widths):
                    header_line += header.ljust(col_widths[i])
            content += header_line + "\n"
            content += "-" * len(header_line) + "\n"

            # Add data rows
            for row in self.current_search_results:
                row_line = ""
                for i, value in enumerate(row):
                    if i < len(col_widths):
                        if value is None:
                            display_value = ""
                        elif hasattr(value, 'strftime'):
                            display_value = value.strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            display_value = str(value)

                        # Truncate if too long
                        if len(display_value) > col_widths[i] - 1:
                            display_value = display_value[:col_widths[i] - 4] + "..."

                        row_line += display_value.ljust(col_widths[i])
                content += row_line + "\n"

            # Insert content
            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)

            # Add print button
            print_btn = tk.Button(print_window, text="Send to Printer",
                                command=lambda: self.send_to_printer(content),
                                bg="#4CAF50", fg="white", font=("Arial", 12, "bold"))
            print_btn.pack(pady=10)

            self.search_status_label.config(text="Print preview opened", fg="blue")

        except Exception as e:
            self.search_status_label.config(text=f"Print error: {str(e)}", fg="red")
            print(f"Print error: {e}")

    def send_to_printer(self, content):
        """Send content to default printer"""
        try:
            import tempfile
            import os

            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
                temp_file.write(content)
                temp_filename = temp_file.name

            # Send to printer (Windows)
            if platform.system() == "Windows":
                os.startfile(temp_filename, "print")
            else:
                # For Linux/Mac
                os.system(f"lpr {temp_filename}")

            self.search_status_label.config(text="Sent to printer", fg="green")

        except Exception as e:
            self.search_status_label.config(text=f"Printer error: {str(e)}", fg="red")
            print(f"Printer error: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = PLCReaderApp(root)
    root.mainloop()




